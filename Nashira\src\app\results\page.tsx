"use client"

import { useSearchParams } from "next/navigation"
import { useEffect, useState } from "react"
import ScrapingResults, { type ScrapedResult } from "~/components/results/ScrapingResults"
import { But<PERSON> } from "~/components/ui/button"
import Link from "next/link"

export default function ResultsPage() {
  const searchParams = useSearchParams()
  const [results, setResults] = useState<ScrapedResult[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const type = searchParams.get("type") || "data"
  const location = searchParams.get("location") || ""
  const category = searchParams.get("category") || ""

  useEffect(() => {
    const loadResults = () => {
      setLoading(true)

      try {
        // Get results from localStorage
        const storedResults = localStorage.getItem('scrapingResults')

        if (!storedResults) {
          setError("No scraping results found")
          setLoading(false)
          return
        }

        const data = JSON.parse(storedResults)

        // Transform Flask results to match the expected format
        const transformedResults: ScrapedResult[] = data.map((result: any) => ({
          name: result.name || "N/A",
          address: result.address || "N/A",
          phone: result.phone || "N/A",
          url: result.url || "",
          category: result.category || category,
          social_links: result.social_links || "N/A",
          is_shopify: result.is_shopify || false,
          is_active: result.is_active || false,
          status: result.status || "Success"
        }))

        setResults(transformedResults)
        setLoading(false)

        // Clear the stored results after loading
        localStorage.removeItem('scrapingResults')
      } catch (err) {
        console.error("Error loading results:", err)
        setError(err instanceof Error ? err.message : "Failed to load results")
        setLoading(false)
      }
    }

    loadResults()
  }, [location, category])

  const getTitle = () => {
    if (type === "wine") {
      return `Wine Data Results for ${category}`
    }
    return `Business Data Results for ${category} in ${location}`
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-[hsl(280,100%,70%)] to-[hsl(240,100%,70%)] flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-white mx-auto mb-4"></div>
          <h2 className="text-2xl font-bold text-white mb-2">Scraping Data...</h2>
          <p className="text-white/70">Please wait while we gather the information</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-[hsl(280,100%,70%)] to-[hsl(240,100%,70%)] flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-white mb-4">Error</h2>
          <p className="text-white/70 mb-6">{error}</p>
          <Link href="/">
            <Button className="bg-white/20 hover:bg-white/30 text-white">
              Go Back Home
            </Button>
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-[hsl(280,100%,70%)] to-[hsl(240,100%,70%)]">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <Link href="/">
            <Button variant="outline" className="mb-4 text-white border-white/20 hover:bg-white/10">
              ← Back to Home
            </Button>
          </Link>
        </div>

        {/* Results */}
        <ScrapingResults data={results} title={getTitle()} />
      </div>
    </div>
  )
}
