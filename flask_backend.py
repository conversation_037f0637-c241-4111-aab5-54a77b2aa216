#!/usr/bin/env python3
"""
Flask Backend for Nashira - Alcohol/Wine Scraper
From Daru to Dolce Vita

This script creates a Flask API that serves as the backend for the alcohol/wine scraper.
It integrates the existing Python scraping functionality and provides API endpoints
for the React frontend to consume.
"""

import os
import json
import time
import asyncio
import re
import socket
import requests
import numpy as np
from typing import List, Dict, Any, Optional, Tuple
from flask import Flask, request, jsonify
from flask_cors import CORS
from concurrent.futures import ThreadPoolExecutor
from urllib.parse import urlparse, parse_qs, unquote
import traceback
import sys
import logging

# Hugging Face imports and authentication
try:
    from transformers.pipelines import pipeline
    from transformers import AutoTokenizer, AutoModel
    from sentence_transformers import SentenceTransformer
    import torch
    import os
    from huggingface_hub import login

    # Set Hugging Face token for authenticated access
    HF_TOKEN = "*************************************"

    # Try multiple authentication methods
    try:
        # Method 1: Use huggingface_hub login
        login(token=HF_TOKEN)
        print("🔑 Hugging Face authentication successful via login()")
    except Exception as login_error:
        print(f"⚠️ Login method failed: {login_error}")
        # Method 2: Set environment variable
        os.environ["HUGGINGFACE_HUB_TOKEN"] = HF_TOKEN
        os.environ["HF_TOKEN"] = HF_TOKEN
        print("🔑 Hugging Face token set via environment variables")

    HUGGINGFACE_AVAILABLE = True
    print("🤖 Hugging Face libraries loaded successfully!")
except ImportError as e:
    print(f"⚠️ Hugging Face libraries not available: {e}")
    print("📦 Install with: pip install transformers sentence-transformers torch huggingface-hub")
    HUGGINGFACE_AVAILABLE = False

# Create Flask app
app = Flask(__name__)

# Enable CORS for all routes
CORS(app, origins=["http://localhost:3000", "http://127.0.0.1:3000"])

# Configuration
MAX_WORKERS = 3  # Limit concurrent requests to avoid overwhelming servers
CACHE_DIR = "cache"

# Ensure cache directory exists
os.makedirs(CACHE_DIR, exist_ok=True)

# AI Models Configuration
class AIModels:
    """Centralized AI models management for Nashira"""

    def __init__(self):
        self.models = {}
        self.load_models()

    def load_models(self):
        """Load Hugging Face models for various AI tasks"""
        if not HUGGINGFACE_AVAILABLE:
            print("⚠️ AI features disabled - Hugging Face not available")
            return

        try:
            print("🤖 Loading AI models for Nashira...")

            # Import here to ensure they're available
            from transformers.pipelines import pipeline
            from sentence_transformers import SentenceTransformer

            # Load lightweight models for faster startup
            # Use smaller, faster models to avoid long download times

            # Sentiment Analysis - using default lightweight model
            try:
                self.models['sentiment'] = pipeline(
                    "sentiment-analysis",
                    return_all_scores=True
                )
                print("✅ Sentiment analysis model loaded")
            except Exception as e:
                print(f"⚠️ Sentiment model failed: {e}")

            # Fill-mask model - using default lightweight model
            try:
                self.models['fill_mask'] = pipeline("fill-mask")
                print("✅ Fill-mask model loaded")
            except Exception as e:
                print(f"⚠️ Fill-mask model failed: {e}")

            # Text Classification for alcohol categorization - using default model
            try:
                self.models['classifier'] = pipeline("zero-shot-classification")
                print("✅ Classification model loaded")
            except Exception as e:
                print(f"⚠️ Classification model failed: {e}")

            # Semantic similarity for smart search - using lightweight model
            try:
                self.models['similarity'] = SentenceTransformer('all-MiniLM-L6-v2')
                print("✅ Semantic similarity model loaded")
            except Exception as e:
                print(f"⚠️ Similarity model failed: {e}")

            # Text generation - using smaller model
            try:
                self.models['text_generator'] = pipeline(
                    "text-generation",
                    model="distilgpt2",
                    max_length=50
                )
                print("✅ Text generation model loaded")
            except Exception as e:
                print(f"⚠️ Text generation model failed: {e}")

            print(f"🎉 Successfully loaded {len(self.models)} AI models!")

        except Exception as e:
            print(f"❌ Error loading AI models: {e}")
            print(f"📋 Full error details: {str(e)}")
            self.models = {}

    def get_model(self, model_name: str):
        """Get a specific model"""
        return self.models.get(model_name)

    def is_available(self) -> bool:
        """Check if AI models are available"""
        return len(self.models) > 0

# Initialize AI models
ai_models = AIModels()

# Import functions from the existing scraper
try:
    from abcd import (
        extract_with_requests,
        scrape_duckduckgo,
        clean_url
    )
    from business_scraper import (
        get_cache_key,
        get_cached_data,
        save_to_cache
    )
    print("Successfully imported functions from abcd.py and business_scraper.py")
except ImportError as e:
    print(f"Error importing required modules: {e}")
    
    # Define fallback functions
    def extract_with_requests(url):
        """Fallback extract_with_requests function if import fails"""
        return {
            "page_title": "Fallback Title",
            "address": "N/A",
            "phone": "N/A",
            "social_links": "N/A"
        }
        
    async def scrape_duckduckgo(queries, max_results=5):
        """Fallback scrape_duckduckgo function if import fails"""
        return []
        
    def clean_url(url):
        """Fallback clean_url function if import fails"""
        if not url or not isinstance(url, str):
            return None
        url = url.strip()
        if not url:
            return None
        if not urlparse(url).scheme:
            return "https://" + url.lstrip('/')
        return url
        
    def get_cache_key(url):
        """Fallback get_cache_key function if import fails"""
        import hashlib
        return hashlib.md5(url.encode()).hexdigest()
        
    def get_cached_data(url):
        """Fallback get_cached_data function if import fails"""
        return None
        
    def save_to_cache(url, data):
        """Fallback save_to_cache function if import fails"""
        pass

# AI-Powered Recommendation Functions
def analyze_sentiment(text: str) -> Dict[str, Any]:
    """Analyze sentiment of reviews or descriptions"""
    if not ai_models.is_available():
        return {"sentiment": "neutral", "confidence": 0.5, "scores": {}}

    try:
        sentiment_model = ai_models.get_model('sentiment')
        if sentiment_model:
            results = sentiment_model(text)
            if results and len(results) > 0:
                # Get the highest scoring sentiment
                best_result = max(results[0], key=lambda x: x['score'])
                return {
                    "sentiment": best_result['label'].lower(),
                    "confidence": best_result['score'],
                    "scores": {r['label'].lower(): r['score'] for r in results[0]}
                }
    except Exception as e:
        print(f"❌ Sentiment analysis error: {e}")

    return {"sentiment": "neutral", "confidence": 0.5, "scores": {}}

def classify_alcohol_type(text: str, candidate_labels: Optional[List[str]] = None) -> Dict[str, Any]:
    """Classify alcohol type from business description"""
    if not ai_models.is_available():
        return {"predicted_type": "unknown", "confidence": 0.0, "scores": {}}

    if candidate_labels is None:
        candidate_labels = [
            "wine", "whiskey", "vodka", "rum", "gin", "beer",
            "champagne", "brandy", "tequila", "liqueur", "spirits"
        ]

    try:
        classifier = ai_models.get_model('classifier')
        if classifier:
            result = classifier(text, candidate_labels)
            return {
                "predicted_type": result['labels'][0],
                "confidence": result['scores'][0],
                "scores": dict(zip(result['labels'], result['scores']))
            }
    except Exception as e:
        print(f"❌ Classification error: {e}")

    return {"predicted_type": "unknown", "confidence": 0.0, "scores": {}}

def calculate_semantic_similarity(query: str, business_descriptions: List[str]) -> List[float]:
    """Calculate semantic similarity between query and business descriptions"""
    if not ai_models.is_available():
        return [0.5] * len(business_descriptions)

    try:
        similarity_model = ai_models.get_model('similarity')
        if similarity_model:
            # Encode query and descriptions
            query_embedding = similarity_model.encode([query])
            desc_embeddings = similarity_model.encode(business_descriptions)

            # Calculate cosine similarity
            similarities = []
            for desc_emb in desc_embeddings:
                similarity = np.dot(query_embedding[0], desc_emb) / (
                    np.linalg.norm(query_embedding[0]) * np.linalg.norm(desc_emb)
                )
                similarities.append(float(similarity))

            return similarities
    except Exception as e:
        print(f"❌ Similarity calculation error: {e}")

    return [0.5] * len(business_descriptions)

def predict_price_range(business_data: Dict[str, Any]) -> Dict[str, Any]:
    """Predict price range based on business information"""
    if not ai_models.is_available():
        return {"predicted_range": "medium", "min_price": 1000, "max_price": 3000}

    try:
        # Create a description for price prediction
        description = f"{business_data.get('name', '')} {business_data.get('description', '')} {business_data.get('address', '')}"

        # Simple heuristic-based price prediction (can be enhanced with ML model)
        description_lower = description.lower()

        # Premium indicators
        premium_keywords = ['premium', 'luxury', 'exclusive', 'vintage', 'aged', 'reserve', 'limited']
        budget_keywords = ['budget', 'affordable', 'cheap', 'discount', 'value']

        premium_score = sum(1 for keyword in premium_keywords if keyword in description_lower)
        budget_score = sum(1 for keyword in budget_keywords if keyword in description_lower)

        if premium_score > budget_score:
            return {"predicted_range": "premium", "min_price": 2000, "max_price": 5000}
        elif budget_score > premium_score:
            return {"predicted_range": "budget", "min_price": 500, "max_price": 1500}
        else:
            return {"predicted_range": "medium", "min_price": 1000, "max_price": 3000}

    except Exception as e:
        print(f"❌ Price prediction error: {e}")

    return {"predicted_range": "medium", "min_price": 1000, "max_price": 3000}

def enhance_business_description(business_name: str, category: str) -> str:
    """Generate enhanced business description using AI"""
    if not ai_models.is_available():
        return f"Quality {category} store offering premium selection"

    try:
        text_generator = ai_models.get_model('text_generator')
        if text_generator:
            prompt = f"{business_name} is a premium {category} store that"
            generated = text_generator(prompt, max_length=50, num_return_sequences=1)
            if generated and len(generated) > 0:
                return generated[0]['generated_text'].replace(prompt, "").strip()
    except Exception as e:
        print(f"❌ Text generation error: {e}")

    return f"Quality {category} store offering premium selection"

def analyze_business_context(business_text: str) -> Dict[str, Any]:
    """Use fill-mask model to understand business context"""
    if not ai_models.is_available():
        return {"context_score": 0.5, "keywords": []}

    try:
        fill_mask = ai_models.get_model('fill_mask')
        if fill_mask:
            # Create masked sentences to understand context
            masked_text = f"This business specializes in [MASK] products."
            results = fill_mask(masked_text)

            keywords = []
            context_score = 0.0

            if results:
                for result in results[:3]:  # Top 3 predictions
                    keywords.append(result['token_str'])
                    context_score += result['score']

                context_score = context_score / len(results)

            return {
                "context_score": context_score,
                "keywords": keywords,
                "analysis": "AI-powered context analysis"
            }
    except Exception as e:
        print(f"❌ Context analysis error: {e}")

    return {"context_score": 0.5, "keywords": []}

def generate_smart_recommendations(user_query: str, results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """Generate AI-powered recommendations based on user query and results"""
    if not results:
        return []

    try:
        print(f"🤖 Generating smart recommendations for: {user_query}")

        # Extract descriptions for similarity calculation
        descriptions = []
        for result in results:
            desc = f"{result.get('name', '')} {result.get('description', '')} {result.get('category', '')}"
            descriptions.append(desc)

        # Calculate semantic similarities
        similarities = calculate_semantic_similarity(user_query, descriptions)

        # Enhance results with AI insights
        enhanced_results = []
        for i, result in enumerate(results):
            enhanced_result = result.copy()

            # Add semantic similarity score
            enhanced_result['similarity_score'] = similarities[i]

            # Add sentiment analysis of description
            description = result.get('description', '')
            sentiment = {"sentiment": "neutral", "confidence": 0.5}
            if description and description != 'N/A':
                sentiment = analyze_sentiment(description)
                enhanced_result['sentiment_analysis'] = sentiment

            # Add alcohol type classification
            business_text = f"{result.get('name', '')} {description}"
            classification = classify_alcohol_type(business_text)
            enhanced_result['ai_classification'] = classification

            # Add business context analysis
            context_analysis = analyze_business_context(business_text)
            enhanced_result['context_analysis'] = context_analysis

            # Add price prediction
            price_prediction = predict_price_range(result)
            enhanced_result['price_prediction'] = price_prediction

            # Generate enhanced description if missing
            if not description or description == 'N/A':
                enhanced_desc = enhance_business_description(
                    result.get('name', ''),
                    result.get('category', 'alcohol')
                )
                enhanced_result['ai_generated_description'] = enhanced_desc

            # Calculate AI recommendation score
            ai_score = (
                similarities[i] * 0.4 +  # Semantic similarity
                enhanced_result.get('completeness_score', 0) / 10 * 0.3 +  # Completeness
                classification['confidence'] * 0.2 +  # Classification confidence
                (1.0 if sentiment.get('sentiment') == 'positive' else 0.5) * 0.1  # Positive sentiment bonus
            )
            enhanced_result['ai_recommendation_score'] = ai_score

            enhanced_results.append(enhanced_result)

        # Sort by AI recommendation score
        enhanced_results.sort(key=lambda x: x.get('ai_recommendation_score', 0), reverse=True)

        print(f"✅ Generated {len(enhanced_results)} smart recommendations")
        return enhanced_results

    except Exception as e:
        print(f"❌ Error generating recommendations: {e}")
        return results

async def get_search_results(query: str, max_results: int = 5) -> List[Dict[str, Any]]:
    """
    Get search results from DuckDuckGo for the given query.
    
    Args:
        query: Search query string
        max_results: Maximum number of results to return
        
    Returns:
        List of search result dictionaries
    """
    try:
        # Use the scrape_duckduckgo function from abcd.py
        results = await scrape_duckduckgo([query], max_results)
        return results
    except Exception as e:
        print(f"Error getting search results: {e}")
        return []

def scrape_url(url: str, category: str, check_shopify: bool = False, check_active: bool = False) -> Optional[Dict[str, Any]]:
    """
    Scrape a single URL using the extract_with_requests function from abcd.py.
    Enhanced for alcohol/wine data extraction.

    Args:
        url: The URL to scrape
        category: The alcohol/wine category or search term
        check_shopify: If True, check if the site is a Shopify site
        check_active: If True, check if the domain is active

    Returns:
        Dictionary with alcohol/wine business data or None if the URL doesn't match filter criteria
    """
    # Check cache first
    cached_data = get_cached_data(url)
    if cached_data:
        # Remove email field from cached data for privacy
        if 'email' in cached_data:
            del cached_data['email']
        return cached_data

    try:
        # Extract data using the function from abcd.py
        data = extract_with_requests(url)
        
        if not data:
            return None

        # Transform data for alcohol/wine business format
        business_data = {
            "name": data.get("page_title", "Unknown Business"),
            "address": data.get("address", "N/A"),
            "phone": data.get("phone", "N/A"),
            "url": url,
            "category": category,
            "social_links": data.get("social_links", "N/A"),
            "is_shopify": "myshopify.com" in url.lower() or "shopify.com" in url.lower(),
            "is_active": True,  # Assume active since we could extract data
            "status": "Success",
            "alcohol_type": category,
            "description": data.get("meta_description", "N/A")
        }

        # Calculate completeness score (prioritize complete information)
        completeness_score = 0
        if business_data["name"] and business_data["name"] != "Unknown Business":
            completeness_score += 2
        if business_data["address"] and business_data["address"] != "N/A":
            completeness_score += 3  # Address is very important for alcohol stores
        if business_data["phone"] and business_data["phone"] != "N/A":
            completeness_score += 2
        if business_data["social_links"] and business_data["social_links"] != "N/A":
            completeness_score += 1

        business_data["completeness_score"] = completeness_score

        # Cache the result
        save_to_cache(url, business_data)
        
        return business_data

    except Exception as e:
        print(f"Error scraping URL {url}: {e}")
        return {
            "name": urlparse(url).netloc,
            "address": "N/A",
            "phone": "N/A",
            "url": url,
            "category": category,
            "social_links": "N/A",
            "is_shopify": False,
            "is_active": False,
            "status": f"Error: {str(e)}",
            "alcohol_type": category,
            "description": "N/A",
            "completeness_score": 0
        }

def scrape_alcohol_data(
    alcohol_type: str,
    location: str,
    country: str,
    max_results: int = 5,
    min_price: int = 500,
    max_price: int = 5000,
    filter_shopify: bool = False,
    filter_active: bool = True
) -> List[Dict[str, Any]]:
    """
    Scrape alcohol/wine data using the abcd.py functions.

    Args:
        alcohol_type: Type of alcohol (wine, whiskey, vodka, etc.)
        location: Location to search in (city, state)
        country: Country for proxy selection
        max_results: Maximum number of results to return (default: 5)
        min_price: Minimum price filter
        max_price: Maximum price filter

    Returns:
        List of alcohol/wine data dictionaries
    """
    try:
        # Construct search query for alcohol/wine stores
        search_query = f"{alcohol_type} stores in {location}, {country}"
        print(f"Searching for: {search_query}")

        # Get search results from DuckDuckGo
        search_results = asyncio.run(get_search_results(search_query, max_results))

        if not search_results:
            print("No search results found.")
            raise Exception("No search results found for the given query")

        # Extract URLs from search results
        urls = []
        for result in search_results:
            url = result.get("Result URL")
            if url:
                clean_result_url = clean_url(url)
                if clean_result_url:
                    urls.append(clean_result_url)

        if not urls:
            print("No valid URLs found in search results.")
            raise Exception("No valid URLs found in search results")

        print(f"Found {len(urls)} URLs to scrape")

        # Use ThreadPoolExecutor for concurrent scraping
        results = []
        with ThreadPoolExecutor(max_workers=MAX_WORKERS) as executor:
            # Submit all scraping tasks
            future_to_url = {
                executor.submit(scrape_url, url, alcohol_type, False, False): url
                for url in urls
            }

            # Process results as they complete
            for future in future_to_url:
                url = future_to_url[future]
                try:
                    result = future.result(timeout=30)  # 30 second timeout per URL
                    if result:
                        results.append(result)
                        print(f"✅ Successfully scraped: {url}")
                    else:
                        print(f"❌ No data extracted from: {url}")
                except Exception as e:
                    print(f"❌ Error scraping {url}: {e}")

        # Generate AI-powered smart recommendations
        if ai_models.is_available():
            print("🤖 Applying AI-powered smart recommendations...")
            smart_results = generate_smart_recommendations(
                f"{alcohol_type} stores in {location}",
                results
            )
        else:
            print("📊 Using traditional sorting (AI not available)")
            # Fallback to traditional sorting by completeness score
            smart_results = sorted(
                results,
                key=lambda x: x.get('completeness_score', 0),
                reverse=True
            )

        # Clean up internal scoring fields for final output
        final_results = []
        for result in smart_results:
            clean_result = result.copy()
            # Remove internal scoring fields but keep AI insights
            if 'completeness_score' in clean_result:
                del clean_result['completeness_score']
            final_results.append(clean_result)

        print(f"✅ Successfully processed {len(final_results)} results with AI enhancement")
        return final_results

    except Exception as e:
        print(f"❌ Error in scrape_alcohol_data: {e}")
        traceback.print_exc()
        return []

@app.route('/api/scrape', methods=['POST'])
def scrape_alcohol():
    """API endpoint for scraping alcohol/wine business data."""
    try:
        # Get JSON data from request
        data = request.get_json()
        
        if not data:
            return jsonify({"error": "No JSON data provided"}), 400

        # Extract parameters
        search_term = data.get('category', '')  # alcohol type
        location = data.get('location', '')
        country = data.get('country', '')
        max_results = data.get('maxResults', 5)
        filter_shopify = data.get('filterShopify', False)
        filter_active = data.get('filterActive', True)

        # Validate required parameters
        if not search_term:
            return jsonify({"error": "Category (alcohol type) is required"}), 400
        if not location:
            return jsonify({"error": "Location is required"}), 400
        if not country:
            return jsonify({"error": "Country is required"}), 400

        # Convert max_results to integer
        try:
            max_results = int(max_results)
            if max_results <= 0 or max_results > 20:
                max_results = 5  # Default to 5 if invalid
        except (ValueError, TypeError):
            max_results = 5

        print(f"🍷 Scraping request: {search_term} in {location}, {country} (max: {max_results})")

        # Scrape alcohol/wine data
        results = scrape_alcohol_data(
            search_term,
            location,
            country,
            max_results,
            filter_shopify=filter_shopify,
            filter_active=filter_active
        )

        # Return results
        return jsonify(results)

    except Exception as e:
        print(f"❌ Error in scrape_alcohol endpoint: {e}")
        traceback.print_exc()
        return jsonify({"error": str(e)}), 500

@app.route('/api/health', methods=['GET'])
def health_check():
    """Health check endpoint with AI status."""
    return jsonify({
        "status": "ok",
        "message": "Nashira Flask backend is running",
        "tagline": "From Daru to Dolce Vita",
        "ai_enabled": ai_models.is_available(),
        "ai_models": list(ai_models.models.keys()) if ai_models.is_available() else []
    })

@app.route('/api/ai/sentiment', methods=['POST'])
def analyze_text_sentiment():
    """AI endpoint for sentiment analysis."""
    try:
        data = request.get_json()
        if not data or 'text' not in data:
            return jsonify({"error": "Text is required"}), 400

        text = data['text']
        sentiment_result = analyze_sentiment(text)

        return jsonify({
            "text": text,
            "sentiment": sentiment_result,
            "ai_enabled": ai_models.is_available()
        })

    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route('/api/ai/classify', methods=['POST'])
def classify_alcohol():
    """AI endpoint for alcohol type classification."""
    try:
        data = request.get_json()
        if not data or 'text' not in data:
            return jsonify({"error": "Text is required"}), 400

        text = data['text']
        candidate_labels = data.get('labels', None)

        classification_result = classify_alcohol_type(text, candidate_labels)

        return jsonify({
            "text": text,
            "classification": classification_result,
            "ai_enabled": ai_models.is_available()
        })

    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route('/api/ai/similarity', methods=['POST'])
def calculate_similarity():
    """AI endpoint for semantic similarity calculation."""
    try:
        data = request.get_json()
        if not data or 'query' not in data or 'texts' not in data:
            return jsonify({"error": "Query and texts are required"}), 400

        query = data['query']
        texts = data['texts']

        if not isinstance(texts, list):
            return jsonify({"error": "Texts must be a list"}), 400

        similarities = calculate_semantic_similarity(query, texts)

        return jsonify({
            "query": query,
            "similarities": similarities,
            "ai_enabled": ai_models.is_available()
        })

    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route('/api/ai/recommend', methods=['POST'])
def get_recommendations():
    """AI endpoint for smart recommendations."""
    try:
        data = request.get_json()
        if not data or 'query' not in data or 'results' not in data:
            return jsonify({"error": "Query and results are required"}), 400

        query = data['query']
        results = data['results']

        if not isinstance(results, list):
            return jsonify({"error": "Results must be a list"}), 400

        recommendations = generate_smart_recommendations(query, results)

        return jsonify({
            "query": query,
            "recommendations": recommendations,
            "ai_enabled": ai_models.is_available(),
            "total_results": len(recommendations)
        })

    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route('/api/ai/enhance-description', methods=['POST'])
def enhance_description():
    """AI endpoint for generating enhanced business descriptions."""
    try:
        data = request.get_json()
        if not data or 'business_name' not in data or 'category' not in data:
            return jsonify({"error": "Business name and category are required"}), 400

        business_name = data['business_name']
        category = data['category']

        enhanced_desc = enhance_business_description(business_name, category)

        return jsonify({
            "business_name": business_name,
            "category": category,
            "enhanced_description": enhanced_desc,
            "ai_enabled": ai_models.is_available()
        })

    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route('/api/ai/analyze-context', methods=['POST'])
def analyze_context():
    """AI endpoint for business context analysis."""
    try:
        data = request.get_json()
        if not data or 'text' not in data:
            return jsonify({"error": "Text is required"}), 400

        text = data['text']
        context_result = analyze_business_context(text)

        return jsonify({
            "text": text,
            "context_analysis": context_result,
            "ai_enabled": ai_models.is_available()
        })

    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route('/api/ai/complete-analysis', methods=['POST'])
def complete_analysis():
    """AI endpoint for complete business analysis combining all AI features."""
    try:
        data = request.get_json()
        if not data or 'business_data' not in data:
            return jsonify({"error": "Business data is required"}), 400

        business_data = data['business_data']

        # Perform complete AI analysis
        business_text = f"{business_data.get('name', '')} {business_data.get('description', '')}"

        analysis_result = {
            "sentiment": analyze_sentiment(business_text),
            "classification": classify_alcohol_type(business_text),
            "context": analyze_business_context(business_text),
            "price_prediction": predict_price_range(business_data),
            "enhanced_description": enhance_business_description(
                business_data.get('name', ''),
                business_data.get('category', 'alcohol')
            ),
            "ai_enabled": ai_models.is_available()
        }

        return jsonify({
            "business_data": business_data,
            "ai_analysis": analysis_result
        })

    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.errorhandler(Exception)
def handle_exception(e):
    """Handle all unhandled exceptions."""
    print(f"Unhandled exception: {str(e)}")
    traceback.print_exc()
    
    # Return JSON instead of HTML for exceptions
    response = jsonify({
        "error": str(e),
        "traceback": traceback.format_exc()
    })
    response.status_code = 500
    return response

if __name__ == '__main__':
    # Run the Flask app
    port = int(os.environ.get('PORT', 5000))
    print("🍷 Starting Nashira Backend - From Daru to Dolce Vita")
    print(f"🌐 Server running on http://localhost:{port}")
    app.run(host='0.0.0.0', port=port, debug=True)
