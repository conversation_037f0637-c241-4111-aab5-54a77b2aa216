import "../styles/globals.css";
import { type Metadata } from "next";
import { TRPCReactProvider } from "~/trpc/react";

export const metadata: Metadata = {
  title: "Nashira - From Daru to Dolce Vita",
  description: "Discover the finest wines and spirits with Nashira's comprehensive alcohol data extraction platform. Search by country, state, city, and alcohol type.",
  icons: [{ rel: "icon", url: "/favicon.ico" }],
};

export default function RootLayout({
  children,
}: Readonly<{ children: React.ReactNode }>) {
  return (
    <html lang="en" className="h-full">
      <body className="h-full bg-white dark:bg-gray-900 text-gray-900 dark:text-white transition-colors duration-300">
        <TRPCReactProvider>
          {children}
        </TRPCReactProvider>
      </body>
    </html>
  );
}
