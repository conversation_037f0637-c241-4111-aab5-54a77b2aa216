"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "~/components/ui/button"
import { Card, CardContent } from "~/components/ui/card"
import { Label } from "~/components/ui/label"
import { Slider } from "~/components/ui/slider"
import { Switch } from "~/components/ui/switch"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "~/components/ui/select"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "~/components/ui/dropdown-menu"
import { Moon, Sun, Wine, Star, Search, Menu, Grape, MapPin, Building2, Crown, Brain, Sparkles } from "lucide-react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { api } from "~/trpc/react"

export default function WineLandingPage() {
  const [isDarkMode, setIsDarkMode] = useState(false)
  const [priceRange, setPriceRange] = useState([500, 2000])
  const [rating, setRating] = useState([3.8])
  const [alcoholType, setAlcoholType] = useState("wine")
  const [country, setCountry] = useState("India")
  const [state, setState] = useState("Maharashtra")
  const [city, setCity] = useState("Mumbai")
  const [maxResults, setMaxResults] = useState(5)
  const [showShopifyOnly, setShowShopifyOnly] = useState(false)
  const [showActiveOnly, setShowActiveOnly] = useState(true)
  const [aiEnabled, setAiEnabled] = useState(false)
  const router = useRouter()

  const toggleDarkMode = () => {
    const newDarkMode = !isDarkMode
    setIsDarkMode(newDarkMode)
    if (newDarkMode) {
      document.documentElement.classList.add("dark")
    } else {
      document.documentElement.classList.remove("dark")
    }
  }

  // tRPC mutation for alcohol/wine scraping
  const scrapeMutation = api.scraper.scrapeBusiness.useMutation({
    onSuccess: (data) => {
      console.log("Alcohol scraping successful:", data)
      // Store results in localStorage and redirect to results page
      localStorage.setItem('scrapingResults', JSON.stringify(data))
      const params = new URLSearchParams({
        type: "alcohol",
        category: alcoholType,
        location: `${city}, ${state}`,
        country: country,
      })
      router.push(`/results?${params.toString()}`)
    },
    onError: (error) => {
      console.error("Alcohol scraping failed:", error)
      alert(`Scraping failed: ${error.message}. Please try again.`)
    },
  })

  const handleStartScraping = () => {
    if (!country || !city) {
      alert("Please select country and city")
      return
    }

    const location = state ? `${city}, ${state}` : city
    scrapeMutation.mutate({
      location: location,
      category: `${alcoholType} stores`,
      country: country,
    })
  }

  const handleAIRecommendations = () => {
    if (!country || !city) {
      alert("Please select country and city for AI recommendations")
      return
    }

    // For now, show a demo alert - this can be connected to actual AI service later
    alert(`🤖 AI Recommendations for ${alcoholType} in ${city}, ${state || country}:\n\n` +
          `• Premium ${alcoholType} stores with 4.5+ ratings\n` +
          `• Best price-to-quality ratio options\n` +
          `• Trending ${alcoholType} varieties in your area\n` +
          `• Seasonal recommendations based on current trends\n\n` +
          `This feature will be fully implemented with real AI analysis soon!`)
  }

  return (
    <div className={`min-h-screen transition-colors duration-300 ${isDarkMode ? "dark" : ""}`}>
      {/* Navigation Bar */}
      <nav className="bg-white/95 dark:bg-gray-900/95 backdrop-blur-md border-b border-gray-200 dark:border-gray-700 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            {/* Logo */}
            <div className="flex items-center">
              <div className="flex-shrink-0 flex items-center">
                <Wine className="h-8 w-8 text-red-600 mr-2" />
                <div>
                  <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Nashira</h1>
                  <p className="text-xs text-gray-600 dark:text-gray-400 italic">From Daru to Dolce Vita</p>
                </div>
              </div>
            </div>

            {/* Desktop Navigation */}
            <div className="hidden md:block">
              <div className="ml-10 flex items-baseline space-x-8">
                <Link
                  href="#"
                  className="text-gray-900 dark:text-white hover:text-red-600 dark:hover:text-red-400 px-3 py-2 text-sm font-medium transition-colors flex items-center"
                >
                  <Wine className="h-4 w-4 mr-1" />
                  Wines
                </Link>
                <Link
                  href="#"
                  className="text-gray-600 dark:text-gray-300 hover:text-red-600 dark:hover:text-red-400 px-3 py-2 text-sm font-medium transition-colors flex items-center"
                >
                  <MapPin className="h-4 w-4 mr-1" />
                  Regions
                </Link>
                <Link
                  href="#"
                  className="text-gray-600 dark:text-gray-300 hover:text-red-600 dark:hover:text-red-400 px-3 py-2 text-sm font-medium transition-colors flex items-center"
                >
                  <Grape className="h-4 w-4 mr-1" />
                  Grapes
                </Link>
                <Link
                  href="#"
                  className="text-gray-600 dark:text-gray-300 hover:text-red-600 dark:hover:text-red-400 px-3 py-2 text-sm font-medium transition-colors flex items-center"
                >
                  <Building2 className="h-4 w-4 mr-1" />
                  Wineries
                </Link>
                <Link
                  href="#"
                  className="text-gray-600 dark:text-gray-300 hover:text-red-600 dark:hover:text-red-400 px-3 py-2 text-sm font-medium transition-colors flex items-center"
                >
                  <Crown className="h-4 w-4 mr-1" />
                  Premium
                </Link>
                <Button variant="outline" className="border-red-600 text-red-600 hover:bg-red-50 dark:hover:bg-red-950">
                  Login
                </Button>
              </div>
            </div>

            {/* Dark Mode Toggle & Mobile Menu */}
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <Sun className="h-4 w-4 text-gray-600 dark:text-gray-300" />
                <Switch
                  checked={isDarkMode}
                  onCheckedChange={toggleDarkMode}
                  className="data-[state=checked]:bg-red-600"
                />
                <Moon className="h-4 w-4 text-gray-600 dark:text-gray-300" />
              </div>

              {/* Mobile menu button */}
              <div className="md:hidden">
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="icon">
                      <Menu className="h-5 w-5" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="w-48">
                    <DropdownMenuItem>Wines</DropdownMenuItem>
                    <DropdownMenuItem>Regions</DropdownMenuItem>
                    <DropdownMenuItem>Grapes</DropdownMenuItem>
                    <DropdownMenuItem>Wineries</DropdownMenuItem>
                    <DropdownMenuItem>Premium</DropdownMenuItem>
                    <DropdownMenuItem>Login</DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
        {/* Background Image with Blur */}
        <div
          className="absolute inset-0 bg-cover bg-center bg-no-repeat"
          style={{
            backgroundImage: `url('/placeholder.svg?height=1080&width=1920')`,
            filter: "blur(8px)",
            transform: "scale(1.1)",
          }}
        />

        {/* Wine-themed Gradient Overlay */}
        <div className="absolute inset-0 bg-gradient-to-br from-red-900/70 via-purple-900/60 to-black/80 dark:from-red-950/80 dark:via-purple-950/70 dark:to-black/90" />

        {/* Content */}
        <div className="relative z-10 max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="max-w-4xl mx-auto">
            {/* Wine Icon */}
            <div className="flex justify-center mb-8">
              <div className="w-20 h-20 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center">
                <Wine className="h-10 w-10 text-white" />
              </div>
            </div>

            {/* Main Headline */}
            <h1 className="text-5xl md:text-7xl font-bold text-white mb-8 leading-tight">
              Discover the Perfect
              <span className="block text-red-300">Spirits & Wines</span>
            </h1>

            <p className="text-xl md:text-2xl text-gray-200 mb-8 max-w-3xl mx-auto">
              From Daru to Dolce Vita - Extract comprehensive alcohol and wine data from global platforms.
              Search by country, state, city, and alcohol type for complete market intelligence.
            </p>

            {/* AI Features Badge */}
            <div className="flex justify-center mb-8">
              <div className="bg-gradient-to-r from-purple-600 to-pink-600 text-white px-6 py-3 rounded-full flex items-center space-x-2 shadow-lg">
                <Brain className="h-5 w-5" />
                <span className="font-semibold">AI-Powered Smart Recommendations</span>
                <Sparkles className="h-5 w-5" />
              </div>
            </div>

            {/* Filter Card */}
            <div className="max-w-6xl mx-auto mb-12">
              <Card className="bg-white/95 dark:bg-gray-800/95 backdrop-blur-sm border-0 shadow-2xl hover:shadow-3xl transition-all duration-300">
                <CardContent className="p-8">
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
                    {/* Country Filter */}
                    <div className="space-y-4">
                      <div className="flex items-center mb-4">
                        <MapPin className="h-5 w-5 text-red-600 mr-2" />
                        <Label className="text-sm font-semibold text-gray-700 dark:text-gray-200">Country</Label>
                      </div>
                      <Select value={country} onValueChange={setCountry}>
                        <SelectTrigger className="w-full h-12 text-base text-gray-900 dark:text-white bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600">
                          <SelectValue placeholder="Select Country" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="India">India</SelectItem>
                          <SelectItem value="United States">United States</SelectItem>
                          <SelectItem value="France">France</SelectItem>
                          <SelectItem value="Italy">Italy</SelectItem>
                          <SelectItem value="Spain">Spain</SelectItem>
                          <SelectItem value="Germany">Germany</SelectItem>
                          <SelectItem value="Australia">Australia</SelectItem>
                          <SelectItem value="Chile">Chile</SelectItem>
                          <SelectItem value="Argentina">Argentina</SelectItem>
                          <SelectItem value="South Africa">South Africa</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    {/* State Filter */}
                    <div className="space-y-4">
                      <div className="flex items-center mb-4">
                        <Building2 className="h-5 w-5 text-red-600 mr-2" />
                        <Label className="text-sm font-semibold text-gray-700 dark:text-gray-200">State/Region</Label>
                      </div>
                      <Select value={state} onValueChange={setState}>
                        <SelectTrigger className="w-full h-12 text-base text-gray-900 dark:text-white bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600">
                          <SelectValue placeholder="Select State/Region" />
                        </SelectTrigger>
                        <SelectContent>
                          {country === "India" && (
                            <>
                              <SelectItem value="Maharashtra">Maharashtra</SelectItem>
                              <SelectItem value="Karnataka">Karnataka</SelectItem>
                              <SelectItem value="Delhi">Delhi</SelectItem>
                              <SelectItem value="Tamil Nadu">Tamil Nadu</SelectItem>
                              <SelectItem value="Gujarat">Gujarat</SelectItem>
                              <SelectItem value="Rajasthan">Rajasthan</SelectItem>
                              <SelectItem value="West Bengal">West Bengal</SelectItem>
                              <SelectItem value="Uttar Pradesh">Uttar Pradesh</SelectItem>
                            </>
                          )}
                          {country === "United States" && (
                            <>
                              <SelectItem value="California">California</SelectItem>
                              <SelectItem value="New York">New York</SelectItem>
                              <SelectItem value="Texas">Texas</SelectItem>
                              <SelectItem value="Florida">Florida</SelectItem>
                              <SelectItem value="Washington">Washington</SelectItem>
                              <SelectItem value="Oregon">Oregon</SelectItem>
                            </>
                          )}
                          {country === "France" && (
                            <>
                              <SelectItem value="Bordeaux">Bordeaux</SelectItem>
                              <SelectItem value="Burgundy">Burgundy</SelectItem>
                              <SelectItem value="Champagne">Champagne</SelectItem>
                              <SelectItem value="Loire Valley">Loire Valley</SelectItem>
                              <SelectItem value="Rhône Valley">Rhône Valley</SelectItem>
                            </>
                          )}
                        </SelectContent>
                      </Select>
                    </div>

                    {/* City Filter */}
                    <div className="space-y-4">
                      <div className="flex items-center mb-4">
                        <MapPin className="h-5 w-5 text-red-600 mr-2" />
                        <Label className="text-sm font-semibold text-gray-700 dark:text-gray-200">City</Label>
                      </div>
                      <Select value={city} onValueChange={setCity}>
                        <SelectTrigger className="w-full h-12 text-base text-gray-900 dark:text-white bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600">
                          <SelectValue placeholder="Select City" />
                        </SelectTrigger>
                        <SelectContent>
                          {state === "Maharashtra" && (
                            <>
                              <SelectItem value="Mumbai">Mumbai</SelectItem>
                              <SelectItem value="Pune">Pune</SelectItem>
                              <SelectItem value="Nashik">Nashik</SelectItem>
                              <SelectItem value="Nagpur">Nagpur</SelectItem>
                            </>
                          )}
                          {state === "Karnataka" && (
                            <>
                              <SelectItem value="Bangalore">Bangalore</SelectItem>
                              <SelectItem value="Mysore">Mysore</SelectItem>
                              <SelectItem value="Hubli">Hubli</SelectItem>
                            </>
                          )}
                          {state === "Delhi" && (
                            <>
                              <SelectItem value="New Delhi">New Delhi</SelectItem>
                              <SelectItem value="Gurgaon">Gurgaon</SelectItem>
                              <SelectItem value="Noida">Noida</SelectItem>
                            </>
                          )}
                          {state === "California" && (
                            <>
                              <SelectItem value="Los Angeles">Los Angeles</SelectItem>
                              <SelectItem value="San Francisco">San Francisco</SelectItem>
                              <SelectItem value="San Diego">San Diego</SelectItem>
                              <SelectItem value="Napa">Napa</SelectItem>
                              <SelectItem value="Sonoma">Sonoma</SelectItem>
                            </>
                          )}
                          {!state && (
                            <SelectItem value="no-state" disabled>Please select a state first</SelectItem>
                          )}
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Alcohol Type Filter */}
                    <div className="space-y-4">
                      <div className="flex items-center mb-4">
                        <Wine className="h-5 w-5 text-red-600 mr-2" />
                        <Label className="text-sm font-semibold text-gray-700 dark:text-gray-200">Alcohol Type</Label>
                      </div>
                      <Select value={alcoholType} onValueChange={setAlcoholType}>
                        <SelectTrigger className="w-full h-12 text-base text-gray-900 dark:text-white bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600">
                          <SelectValue placeholder="Select Alcohol Type" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="wine">Wine</SelectItem>
                          <SelectItem value="whiskey">Whiskey</SelectItem>
                          <SelectItem value="vodka">Vodka</SelectItem>
                          <SelectItem value="rum">Rum</SelectItem>
                          <SelectItem value="gin">Gin</SelectItem>
                          <SelectItem value="beer">Beer</SelectItem>
                          <SelectItem value="champagne">Champagne</SelectItem>
                          <SelectItem value="brandy">Brandy</SelectItem>
                          <SelectItem value="tequila">Tequila</SelectItem>
                          <SelectItem value="liqueur">Liqueur</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Number of Results Filter */}
                    <div className="space-y-4">
                      <div className="flex items-center mb-4">
                        <Search className="h-5 w-5 text-red-600 mr-2" />
                        <Label className="text-sm font-semibold text-gray-700 dark:text-gray-200">Max Results</Label>
                      </div>
                      <Select value={maxResults.toString()} onValueChange={(value) => setMaxResults(parseInt(value))}>
                        <SelectTrigger className="w-full h-12 text-base text-gray-900 dark:text-white bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600">
                          <SelectValue placeholder="Select Max Results" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="3">3 Results</SelectItem>
                          <SelectItem value="5">5 Results</SelectItem>
                          <SelectItem value="10">10 Results</SelectItem>
                          <SelectItem value="15">15 Results</SelectItem>
                          <SelectItem value="20">20 Results</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>


                  {/* Additional Filters Row */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-8 pt-6 border-t border-gray-200 dark:border-gray-600">
                    {/* Price Range Filter */}
                    <div className="space-y-4">
                      <div className="flex items-center mb-4">
                        <span className="text-red-600 mr-2 font-bold">₹</span>
                        <Label className="text-sm font-semibold text-gray-700 dark:text-gray-200">Price Range</Label>
                      </div>
                      <div className="space-y-4 pt-2">
                        <Slider
                          value={priceRange}
                          onValueChange={setPriceRange}
                          max={5000}
                          min={500}
                          step={100}
                          className="w-full"
                        />
                        <div className="flex justify-between text-sm text-gray-600 dark:text-gray-400">
                          <span className="font-medium">₹{priceRange[0]}</span>
                          <span className="font-medium">₹{priceRange[1]}</span>
                        </div>
                      </div>
                    </div>

                    {/* Rating Filter */}
                    <div className="space-y-4">
                      <div className="flex items-center mb-4">
                        <Star className="h-5 w-5 text-red-600 mr-2 fill-current" />
                        <Label className="text-sm font-semibold text-gray-700 dark:text-gray-200">Minimum Rating</Label>
                      </div>
                      <div className="space-y-4 pt-2">
                        <Slider
                          value={rating}
                          onValueChange={setRating}
                          max={5}
                          min={3.0}
                          step={0.1}
                          className="w-full"
                        />
                        <div className="flex justify-between text-sm text-gray-600 dark:text-gray-400">
                          <span className="font-medium">{rating[0]} stars</span>
                          <span className="font-medium">5+ stars</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Advanced Options */}
                  <div className="mt-8 pt-6 border-t border-gray-200 dark:border-gray-600">
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Advanced Options</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      {/* Shopify Filter */}
                      <div className="flex items-center space-x-3">
                        <Switch
                          checked={showShopifyOnly}
                          onCheckedChange={setShowShopifyOnly}
                          className="data-[state=checked]:bg-red-600"
                        />
                        <div>
                          <Label className="text-sm font-medium text-gray-700 dark:text-gray-200">
                            Shopify Stores Only
                          </Label>
                          <p className="text-xs text-gray-500 dark:text-gray-400">
                            Show only e-commerce stores powered by Shopify
                          </p>
                        </div>
                      </div>

                      {/* Active Domains Filter */}
                      <div className="flex items-center space-x-3">
                        <Switch
                          checked={showActiveOnly}
                          onCheckedChange={setShowActiveOnly}
                          className="data-[state=checked]:bg-red-600"
                        />
                        <div>
                          <Label className="text-sm font-medium text-gray-700 dark:text-gray-200">
                            Active Domains Only
                          </Label>
                          <p className="text-xs text-gray-500 dark:text-gray-400">
                            Filter out inactive or unreachable websites
                          </p>
                        </div>
                      </div>
                    </div>

                    {/* AI Enhancement Section */}
                    <div className="mt-6 p-4 bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 rounded-lg border border-purple-200 dark:border-purple-700">
                      <div className="flex items-center space-x-3 mb-3">
                        <Brain className="h-5 w-5 text-purple-600" />
                        <h4 className="text-sm font-semibold text-purple-900 dark:text-purple-100">
                          AI-Powered Enhancements
                        </h4>
                      </div>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-xs text-purple-700 dark:text-purple-200">
                        <div className="flex items-center space-x-2">
                          <Sparkles className="h-3 w-3" />
                          <span>Sentiment Analysis of Reviews</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Sparkles className="h-3 w-3" />
                          <span>Smart Alcohol Classification</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Sparkles className="h-3 w-3" />
                          <span>Semantic Search Matching</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Sparkles className="h-3 w-3" />
                          <span>Intelligent Price Prediction</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Action Buttons */}
                  <div className="mt-8 text-center space-y-4">
                    <div className="flex flex-col sm:flex-row gap-4 justify-center">
                      <Button
                        size="lg"
                        className="bg-gradient-to-r from-red-600 to-purple-600 hover:from-red-700 hover:to-purple-700 text-white px-12 py-6 text-xl font-semibold rounded-xl shadow-2xl hover:shadow-red-500/25 transition-all duration-300 transform hover:scale-105"
                        onClick={handleStartScraping}
                        disabled={scrapeMutation.isPending || !country || !city}
                      >
                        <Search className="mr-3 h-6 w-6" />
                        {scrapeMutation.isPending ? "Searching for Spirits..." : "Discover Spirits"}
                      </Button>

                      <Button
                        size="lg"
                        variant="outline"
                        className="border-2 border-purple-600 text-purple-600 hover:bg-purple-600 hover:text-white dark:border-purple-400 dark:text-purple-400 dark:hover:bg-purple-400 dark:hover:text-gray-900 px-8 py-6 text-lg font-semibold rounded-xl shadow-lg transition-all duration-300 transform hover:scale-105"
                        onClick={handleAIRecommendations}
                        disabled={!country || !city}
                      >
                        <Brain className="mr-3 h-6 w-6" />
                        AI Recommendations
                      </Button>
                    </div>

                    {(!country || !city) && (
                      <p className="text-sm text-gray-500 dark:text-gray-400 mt-2">
                        Please select country and city to start searching
                      </p>
                    )}
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Additional Info */}
            <div className="flex flex-wrap justify-center items-center gap-8 text-gray-300 text-sm">
              <div className="flex items-center">
                <Star className="h-4 w-4 text-yellow-400 mr-1 fill-current" />
                <span>4.9/5 Rating</span>
              </div>
              <div className="flex items-center">
                <Wine className="h-4 w-4 text-red-400 mr-1" />
                <span>1M+ Wines Tracked</span>
              </div>
              <div className="flex items-center">
                <Building2 className="h-4 w-4 text-purple-400 mr-1" />
                <span>10K+ Wineries</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-gray-50 dark:bg-gray-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">AI-Powered Wine Intelligence</h2>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
              Advanced AI-driven platform combining machine learning with comprehensive data extraction.
              Trusted by sommeliers, wine retailers, and industry experts worldwide.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <Card className="bg-white dark:bg-gray-800 border-0 shadow-lg hover:shadow-xl transition-all duration-300 group">
              <CardContent className="p-8 text-center">
                <div className="w-16 h-16 bg-purple-100 dark:bg-purple-900 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform">
                  <Brain className="h-8 w-8 text-purple-600" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">AI-Powered Intelligence</h3>
                <p className="text-gray-600 dark:text-gray-300">
                  Advanced machine learning models analyze sentiment, classify alcohol types, predict prices, and provide
                  semantic search matching for superior recommendations.
                </p>
              </CardContent>
            </Card>

            <Card className="bg-white dark:bg-gray-800 border-0 shadow-lg hover:shadow-xl transition-all duration-300 group">
              <CardContent className="p-8 text-center">
                <div className="w-16 h-16 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform">
                  <Star className="h-8 w-8 text-green-600 fill-current" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">Real-time Pricing</h3>
                <p className="text-gray-600 dark:text-gray-300">
                  Monitor wine prices across multiple platforms and regions. Get instant alerts on price changes and
                  market trends.
                </p>
              </CardContent>
            </Card>

            <Card className="bg-white dark:bg-gray-800 border-0 shadow-lg hover:shadow-xl transition-all duration-300 group">
              <CardContent className="p-8 text-center">
                <div className="w-16 h-16 bg-purple-100 dark:bg-purple-900 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform">
                  <Grape className="h-8 w-8 text-purple-600" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">Expert Analytics</h3>
                <p className="text-gray-600 dark:text-gray-300">
                  Advanced analytics and insights to help you make informed decisions about wine investments and
                  inventory management.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 bg-gradient-to-r from-red-600 to-purple-600 dark:from-red-700 dark:to-purple-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center text-white">
            <div>
              <div className="text-4xl font-bold mb-2">1M+</div>
              <div className="text-red-100">Wines Tracked</div>
            </div>
            <div>
              <div className="text-4xl font-bold mb-2">10K+</div>
              <div className="text-red-100">Wineries</div>
            </div>
            <div>
              <div className="text-4xl font-bold mb-2">50+</div>
              <div className="text-red-100">Countries</div>
            </div>
            <div>
              <div className="text-4xl font-bold mb-2">99.9%</div>
              <div className="text-red-100">Accuracy</div>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}
