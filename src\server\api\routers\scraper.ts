import { z } from "zod";
import { TRPCError } from "@trpc/server";
import { createTRPCRouter, publicProcedure } from "~/server/api/trpc";
import Papa from "papaparse";

export const scraperRouter = createTRPCRouter({
  uploadCsv: publicProcedure
    .input(
      z.object({
        fileName: z.string(),
        fileContent: z.string(), // base64 string
      })
    )
    .mutation(async ({ input }) => {
      try {
        const buffer = Buffer.from(input.fileContent, "base64");
        const text = buffer.toString("utf-8");

        const parsed = Papa.parse(text, {
          header: true,
          skipEmptyLines: true,
        });

        if (parsed.errors.length) {
          console.error("CSV parsing errors:", parsed.errors);
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: "Failed to parse CSV",
          });
        }

        // Map parsed data to ScrapedData[] type
        const results = (parsed.data as any[]).map((row) => ({
          url: row.url ?? "",
          title: row.title ?? "",
          description: row.description ?? "",
          contactInfo: {
            phone: row.phone ?? "",
            email: row.email ?? "",
          },
        }));

        return results;
      } catch (error) {
        console.error("Upload processing error:", error);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to process uploaded file",
        });
      }
    }),

  scrapeBusiness: publicProcedure
    .input(
      z.object({
        category: z.string(),
        location: z.string(),
        country: z.string(),
        maxResults: z.number().min(1).max(100).default(10),
        showShopifyOnly: z.boolean().default(false),
        showActiveOnly: z.boolean().default(true),
        priceRange: z.array(z.number()).length(2).optional(),
        rating: z.array(z.number()).length(1).optional(),
      })
    )
    .mutation(async ({ input }) => {
      try {
        // TODO: Implement actual scraping logic here
        // For now, return mock data to prevent the error
        console.log("Business scraping request:", input);

        // Simulate processing time
        await new Promise(resolve => setTimeout(resolve, 2000));

        // Return mock data structure that matches expected format
        const mockResults = Array.from({ length: Math.min(input.maxResults, 5) }, (_, i) => ({
          id: `wine-${i + 1}`,
          name: `${input.category} Store ${i + 1}`,
          description: `Premium ${input.category} retailer in ${input.location}`,
          url: `https://example-wine-store-${i + 1}.com`,
          address: `${i + 1} Wine Street, ${input.location}, ${input.country}`,
          phone: `******-${String(i + 1).padStart(3, '0')}-${String(Math.floor(Math.random() * 10000)).padStart(4, '0')}`,
          email: `contact@wine-store-${i + 1}.com`,
          rating: input.rating?.[0] ? input.rating[0] + (Math.random() * 0.5) : 4.0 + (Math.random() * 1.0),
          priceRange: input.priceRange || [500, 2000],
          category: input.category,
          isShopify: input.showShopifyOnly ? true : Math.random() > 0.5,
          isActive: input.showActiveOnly ? true : Math.random() > 0.3,
          lastUpdated: new Date().toISOString(),
        }));

        return {
          results: mockResults,
          totalFound: mockResults.length,
          searchParams: input,
        };
      } catch (error) {
        console.error("Business scraping error:", error);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to scrape business data",
        });
      }
    }),
});
