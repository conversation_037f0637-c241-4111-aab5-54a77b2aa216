<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nashira - Wine Scraper Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
        }
        h1 {
            text-align: center;
            margin-bottom: 10px;
        }
        .tagline {
            text-align: center;
            font-style: italic;
            margin-bottom: 30px;
            opacity: 0.9;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select {
            width: 100%;
            padding: 10px;
            border: none;
            border-radius: 5px;
            font-size: 16px;
        }
        button {
            background: #ff6b6b;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            width: 100%;
            margin-top: 10px;
        }
        button:hover {
            background: #ff5252;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .results {
            margin-top: 30px;
            padding: 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            display: none;
        }
        .result-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            margin-bottom: 15px;
            border-radius: 8px;
        }
        .loading {
            text-align: center;
            font-style: italic;
        }
        .error {
            color: #ff6b6b;
            background: rgba(255, 107, 107, 0.1);
            padding: 10px;
            border-radius: 5px;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🍷 Nashira</h1>
        <p class="tagline">From Daru to Dolce Vita</p>
        
        <form id="scrapeForm">
            <div class="form-group">
                <label for="category">Business Category:</label>
                <input type="text" id="category" name="category" placeholder="e.g., wine stores, liquor shops" required>
            </div>
            
            <div class="form-group">
                <label for="location">Location:</label>
                <input type="text" id="location" name="location" placeholder="e.g., New York, London" required>
            </div>
            
            <div class="form-group">
                <label for="country">Country:</label>
                <select id="country" name="country" required>
                    <option value="">Select Country</option>
                    <option value="United States">United States</option>
                    <option value="United Kingdom">United Kingdom</option>
                    <option value="Canada">Canada</option>
                    <option value="Australia">Australia</option>
                    <option value="Germany">Germany</option>
                    <option value="France">France</option>
                    <option value="Italy">Italy</option>
                    <option value="Spain">Spain</option>
                    <option value="India">India</option>
                </select>
            </div>
            
            <button type="submit" id="submitBtn">🔍 Search Wine Businesses</button>
        </form>
        
        <div id="results" class="results">
            <h3>Search Results:</h3>
            <div id="resultsContent"></div>
        </div>
    </div>

    <script>
        const form = document.getElementById('scrapeForm');
        const submitBtn = document.getElementById('submitBtn');
        const results = document.getElementById('results');
        const resultsContent = document.getElementById('resultsContent');

        form.addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const formData = new FormData(form);
            const data = {
                category: formData.get('category'),
                location: formData.get('location'),
                country: formData.get('country')
            };

            // Show loading state
            submitBtn.disabled = true;
            submitBtn.textContent = '🔄 Searching...';
            results.style.display = 'block';
            resultsContent.innerHTML = '<div class="loading">Searching for wine businesses... This may take a few moments.</div>';

            try {
                // Test Django backend health first
                const healthResponse = await fetch('http://localhost:8000/api/health/');
                if (!healthResponse.ok) {
                    throw new Error('Django backend is not running');
                }

                // Make the scraping request
                const response = await fetch('http://localhost:8000/api/wine/scrape/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        category: data.category,
                        location: data.location,
                        country: data.country,
                        alcohol_type: data.category.replace(' stores', '').replace(' shops', ''),
                        max_results: 10,
                        sort_by_shopify: false,
                        show_active_only: true
                    })
                });

                if (!response.ok) {
                    const errorText = await response.text();
                    throw new Error(`Server error: ${response.status} - ${errorText}`);
                }

                const result = await response.json();
                
                if (result.results && result.results.length > 0) {
                    let html = `<p><strong>Found ${result.results.length} businesses:</strong></p>`;
                    
                    result.results.forEach((business, index) => {
                        html += `
                            <div class="result-item">
                                <h4>${business.name || `Business ${index + 1}`}</h4>
                                <p><strong>Address:</strong> ${business.address || 'Not available'}</p>
                                <p><strong>Phone:</strong> ${business.phone || 'Not available'}</p>
                                <p><strong>Email:</strong> ${business.email || 'Not available'}</p>
                                <p><strong>Website:</strong> ${business.website ? `<a href="${business.website}" target="_blank" style="color: #87ceeb;">${business.website}</a>` : 'Not available'}</p>
                                ${business.description ? `<p><strong>Description:</strong> ${business.description}</p>` : ''}
                            </div>
                        `;
                    });
                    
                    resultsContent.innerHTML = html;
                } else {
                    resultsContent.innerHTML = '<div class="error">No businesses found for your search criteria. Try different keywords or location.</div>';
                }

            } catch (error) {
                console.error('Error:', error);
                resultsContent.innerHTML = `<div class="error">Error: ${error.message}<br><br>Make sure the Django backend is running on http://localhost:8000</div>`;
            } finally {
                submitBtn.disabled = false;
                submitBtn.textContent = '🔍 Search Wine Businesses';
            }
        });
    </script>
</body>
</html>
